# Anleitung zur Bearbeitung der Musik-Daten

## Übersicht
Die Musikseite (janabreitmar.de/musik) zeigt eine interaktive Liste aller Ihrer Lieder mit einem integrierten Audio-Player. Alle Informationen über die Lieder werden in der Datei `musik-data.js` gespeichert.

## Wie bearbeite ich die Lieder-Informationen?

### 1. <PERSON>i öffnen
Öffnen Sie die Datei `musik-data.js` in einem Texteditor (z.B. Notepad, TextEdit, oder einem Code-Editor).

### 2. Struktur verstehen
Jedes Lied ist als ein Objekt in der Liste `MUSIK_DATEN` gespeichert:

```javascript
{
    titel: "Titel des Liedes",
    genre: "Genre (z.B. Ambient, Tribal, Pop, Electronic, Sleep)",
    album: "Album-Name",
    sprache: "Sprache (Deutsch, Englisch, Spanisch)",
    dateiname: "Exakter Dateiname.wav",
    lyrics: "Liedtext hier (optional)"
}
```

### 3. Informationen bearbeiten

#### Titel ändern:
```javascript
titel: "Neuer Titel",
```

#### Genre ändern:
```javascript
genre: "Neues Genre",
```
Verfügbare Genres: Ambient, Tribal, Pop, Electronic, Sleep

#### Album ändern:
```javascript
album: "Neuer Album-Name",
```

#### Sprache ändern:
```javascript
sprache: "Neue Sprache",
```
Verfügbare Sprachen: Deutsch, Englisch, Spanisch

#### Lyrics hinzufügen/ändern:
```javascript
lyrics: "Hier steht der komplette Liedtext. 
Neue Zeilen können mit \\n gemacht werden.
Das ist die zweite Zeile."
```

### 4. Neues Lied hinzufügen

1. Laden Sie die Audio-Datei (.wav) in den `audio/` Ordner hoch
2. Fügen Sie am Ende der Liste (vor der schließenden `]`) ein neues Objekt hinzu:

```javascript
,
{
    titel: "Neues Lied",
    genre: "Ambient",
    album: "Neues Album",
    sprache: "Deutsch",
    dateiname: "Neues Lied.wav",
    lyrics: ""
}
```

**WICHTIG:** Vergessen Sie nicht das Komma vor dem neuen Eintrag!

### 5. Lied entfernen
Löschen Sie das komplette Objekt inklusive der umgebenden geschweiften Klammern `{ }` und das Komma.

### 6. Reihenfolge ändern
Die Lieder werden in der Reihenfolge angezeigt, wie sie in der Liste stehen. Verschieben Sie einfach die kompletten Objekte nach oben oder unten.

## Wichtige Regeln

1. **Anführungszeichen:** Alle Texte müssen in Anführungszeichen stehen: `"Text"`
2. **Kommas:** Jede Zeile (außer der letzten in einem Objekt) muss mit einem Komma enden
3. **Dateiname:** Muss exakt mit der Datei im `audio/` Ordner übereinstimmen
4. **Sonderzeichen:** Bei Umlauten (ä, ö, ü) und anderen Sonderzeichen aufpassen

## Beispiel für eine Bearbeitung

**Vorher:**
```javascript
{
    titel: "Like Water",
    genre: "Ambient",
    album: "Inner Strength",
    sprache: "Englisch",
    dateiname: "Like Water.wav",
    lyrics: ""
}
```

**Nachher (mit Lyrics):**
```javascript
{
    titel: "Like Water",
    genre: "Ambient",
    album: "Inner Strength",
    sprache: "Englisch",
    dateiname: "Like Water.wav",
    lyrics: "Flowing like water through my soul\\nGentle waves of peace and calm\\nIn this moment I am whole"
}
```

## Fehlerbehebung

### Seite lädt nicht richtig
- Überprüfen Sie, ob alle Anführungszeichen und Kommas korrekt gesetzt sind
- Stellen Sie sicher, dass die letzte Zeile in jedem Objekt KEIN Komma hat
- Überprüfen Sie, ob alle geschweiften Klammern `{ }` geschlossen sind

### Lied spielt nicht ab
- Überprüfen Sie, ob der Dateiname exakt mit der Datei im `audio/` Ordner übereinstimmt
- Stellen Sie sicher, dass die Audio-Datei im .wav Format vorliegt

### Sonderzeichen werden falsch angezeigt
- Verwenden Sie für Umlaute die HTML-Codes: ä = &auml;, ö = &ouml;, ü = &uuml;
- Oder stellen Sie sicher, dass die Datei in UTF-8 kodiert gespeichert wird

## Backup
Erstellen Sie immer eine Kopie der `musik-data.js` Datei, bevor Sie Änderungen vornehmen!
